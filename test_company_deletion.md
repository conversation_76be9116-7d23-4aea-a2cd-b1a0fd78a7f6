# Company Deletion Testing Guide

## Prerequisites

1. **Apply Database Constraints**: First, run the SQL script `manual_company_cascade_constraints.sql` in the Supabase SQL Editor to add the missing foreign key constraints.

2. **Verify Environment Variables**: Check that the Edge Function has access to the required environment variables in the Supabase Functions dashboard.

## Testing Steps

### 1. Create a Test Company (Optional)
If you want to test with a dedicated test company:

```sql
-- Create a test company
INSERT INTO public.companies (name, email, phone, address, status)
VALUES ('Test Company for Deletion', '<EMAIL>', '+972-50-000-0000', 'Test Address', 'active')
RETURNING id;
```

### 2. Add Test Data to the Company
Create some test data associated with the company:

```sql
-- Replace 'YOUR_COMPANY_ID' with the actual company ID
-- Add a test user role
INSERT INTO public.user_roles (user_id, company_id, role)
VALUES ('YOUR_USER_ID', 'YOUR_COMPANY_ID', 'company_admin');

-- Add a test lead
INSERT INTO public.leads (full_name, phone, email, company_id, user_id)
VALUES ('Test Lead', '+972-50-000-0001', '<EMAIL>', 'YOUR_COMPANY_ID', 'YOUR_USER_ID');

-- Add a test case type
INSERT INTO public.case_types (name, hourly_rate, company_id)
VALUES ('Test Case Type', 500.00, 'YOUR_COMPANY_ID');
```

### 3. Test Company Deletion

#### Method 1: Through the UI
1. Log in as a super admin user
2. Navigate to the company management section
3. Find the test company
4. Click the delete button
5. Confirm the deletion

#### Method 2: Direct API Test
Use the browser console or a tool like Postman to test the Edge Function directly:

```javascript
// In browser console (while logged in as super admin)
const response = await supabase.functions.invoke('company-deletion', {
  body: { companyId: 'YOUR_COMPANY_ID' }
});

console.log('Response:', response);
```

### 4. Verify Deletion Results

After deletion, verify that all related data has been removed:

```sql
-- Check that the company is deleted
SELECT * FROM public.companies WHERE id = 'YOUR_COMPANY_ID';

-- Check that related data is deleted
SELECT * FROM public.user_roles WHERE company_id = 'YOUR_COMPANY_ID';
SELECT * FROM public.leads WHERE company_id = 'YOUR_COMPANY_ID';
SELECT * FROM public.cases WHERE company_id = 'YOUR_COMPANY_ID';
SELECT * FROM public.case_types WHERE company_id = 'YOUR_COMPANY_ID';
SELECT * FROM public.whatsapp_conversations WHERE company_id = 'YOUR_COMPANY_ID';

-- All queries above should return 0 rows
```

### 5. Check Edge Function Logs

Monitor the Edge Function logs in the Supabase Dashboard:
1. Go to Functions → company-deletion
2. Click on "Logs" tab
3. Look for detailed execution logs showing each step of the deletion process

## Expected Behavior

### Success Case
- Status: 200 OK
- Response: `{ "success": true, "message": "Company ... deleted successfully", "deletedUsers": N }`
- All related database records removed
- All associated files deleted from storage
- All company users removed from Supabase Auth

### Error Cases
- **401 Unauthorized**: Missing or invalid authentication token
- **403 Forbidden**: User is not a super admin
- **404 Not Found**: Company ID doesn't exist
- **500 Internal Server Error**: Database constraints, environment variables, or other server issues

## Troubleshooting

### If you get a 500 error:
1. Check the Edge Function logs for specific error messages
2. Verify environment variables are set correctly
3. Ensure the database constraints were applied successfully
4. Check that the user has super admin role in the database

### If deletion is incomplete:
1. Check the logs to see which step failed
2. Manually verify which data remains in the database
3. The Edge Function includes manual deletion steps that should work even without CASCADE constraints

### If authentication fails:
1. Verify the user is logged in and has a valid session
2. Check that the user has the 'super_admin' role in the user_roles table
3. Ensure the authorization header is being passed correctly

## Monitoring

After successful testing, monitor the production environment for:
- Edge Function execution time (should be under 10 seconds for most companies)
- Error rates in the function logs
- Database performance during large company deletions
- Storage cleanup effectiveness
