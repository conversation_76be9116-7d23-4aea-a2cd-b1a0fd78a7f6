import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.56.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface DeleteCompanyRequest {
  companyId: string;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client with service role key for admin operations
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify the user is authenticated
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if user is super admin
    const { data: userRole, error: roleError } = await supabaseAdmin
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .eq('role', 'super_admin')
      .single()

    if (roleError || !userRole) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized: Only super admins can delete companies' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const { companyId }: DeleteCompanyRequest = await req.json()

    if (!companyId) {
      return new Response(
        JSON.stringify({ error: 'Company ID is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Starting company deletion process for company: ${companyId}`)

    // Step 1: Verify company exists
    const { data: company, error: companyError } = await supabaseAdmin
      .from('companies')
      .select('id, name')
      .eq('id', companyId)
      .single()

    if (companyError || !company) {
      return new Response(
        JSON.stringify({ error: 'Company not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Found company: ${company.name} (${company.id})`)

    // Step 2: Get all users associated with this company
    const { data: companyUsers, error: usersError } = await supabaseAdmin
      .from('user_roles')
      .select('user_id')
      .eq('company_id', companyId)

    if (usersError) {
      console.error('Error fetching company users:', usersError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch company users' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Found ${companyUsers?.length || 0} users to delete`)

    // Step 3: Delete all files from storage buckets for this company
    try {
      // Get all case documents for this company
      const { data: caseDocuments, error: documentsError } = await supabaseAdmin
        .from('case_documents')
        .select('file_path')
        .eq('company_id', companyId)

      if (!documentsError && caseDocuments) {
        console.log(`Found ${caseDocuments.length} case documents to delete`)
        
        // Delete each file from storage
        for (const doc of caseDocuments) {
          const { error: storageError } = await supabaseAdmin.storage
            .from('case-documents')
            .remove([doc.file_path])
          
          if (storageError) {
            console.error(`Error deleting file ${doc.file_path}:`, storageError)
          } else {
            console.log(`Deleted file: ${doc.file_path}`)
          }
        }
      }
    } catch (error) {
      console.error('Error cleaning up storage files:', error)
      // Continue with deletion even if storage cleanup fails
    }

    // Step 4: Delete all users from Supabase Auth
    if (companyUsers && companyUsers.length > 0) {
      for (const userRole of companyUsers) {
        try {
          const { error: deleteUserError } = await supabaseAdmin.auth.admin.deleteUser(
            userRole.user_id
          )
          
          if (deleteUserError) {
            console.error(`Error deleting user ${userRole.user_id}:`, deleteUserError)
          } else {
            console.log(`Deleted user from auth: ${userRole.user_id}`)
          }
        } catch (error) {
          console.error(`Failed to delete user ${userRole.user_id}:`, error)
          // Continue with other users even if one fails
        }
      }
    }

    // Step 5: Delete the company (this will cascade delete all related data due to foreign key constraints)
    const { error: deleteCompanyError } = await supabaseAdmin
      .from('companies')
      .delete()
      .eq('id', companyId)

    if (deleteCompanyError) {
      console.error('Error deleting company:', deleteCompanyError)
      return new Response(
        JSON.stringify({ 
          error: 'Failed to delete company', 
          details: deleteCompanyError.message 
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Successfully deleted company: ${company.name} (${companyId})`)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Company "${company.name}" and all associated data deleted successfully`,
        deletedUsers: companyUsers?.length || 0
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Unexpected error in company deletion:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
