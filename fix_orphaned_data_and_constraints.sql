-- Fix Orphaned Data and Add Company Cascade Constraints
-- This script first cleans up orphaned records, then adds the foreign key constraints

-- Step 1: Identify and clean up orphaned records
-- These are records that reference company_ids that don't exist in the companies table

-- Check for orphaned case_types
SELECT 'case_types' as table_name, COUNT(*) as orphaned_count
FROM public.case_types ct
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = ct.company_id);

-- Check for orphaned cases
SELECT 'cases' as table_name, COUNT(*) as orphaned_count
FROM public.cases ca
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = ca.company_id);

-- Check for orphaned case_tasks
SELECT 'case_tasks' as table_name, COUNT(*) as orphaned_count
FROM public.case_tasks ct
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = ct.company_id);

-- Check for orphaned case_time_entries
SELECT 'case_time_entries' as table_name, COUNT(*) as orphaned_count
FROM public.case_time_entries cte
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = cte.company_id);

-- Check for orphaned case_documents
SELECT 'case_documents' as table_name, COUNT(*) as orphaned_count
FROM public.case_documents cd
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = cd.company_id);

-- Check for orphaned whatsapp_conversations
SELECT 'whatsapp_conversations' as table_name, COUNT(*) as orphaned_count
FROM public.whatsapp_conversations wc
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = wc.company_id);

-- Check for orphaned task_reminders
SELECT 'task_reminders' as table_name, COUNT(*) as orphaned_count
FROM public.task_reminders tr
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = tr.company_id);

-- Step 2: Delete orphaned records
-- WARNING: This will permanently delete data that references non-existent companies

-- Delete orphaned case_types
DELETE FROM public.case_types 
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = case_types.company_id);

-- Delete orphaned cases
DELETE FROM public.cases 
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = cases.company_id);

-- Delete orphaned case_tasks
DELETE FROM public.case_tasks 
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = case_tasks.company_id);

-- Delete orphaned case_time_entries
DELETE FROM public.case_time_entries 
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = case_time_entries.company_id);

-- Delete orphaned case_documents
DELETE FROM public.case_documents 
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = case_documents.company_id);

-- Delete orphaned whatsapp_conversations
DELETE FROM public.whatsapp_conversations 
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = whatsapp_conversations.company_id);

-- Delete orphaned task_reminders
DELETE FROM public.task_reminders 
WHERE NOT EXISTS (SELECT 1 FROM public.companies c WHERE c.id = task_reminders.company_id);

-- Step 3: Now add the foreign key constraints (should work after cleanup)

-- Add foreign key constraint for case_types.company_id
ALTER TABLE public.case_types 
DROP CONSTRAINT IF EXISTS case_types_company_id_fkey;

ALTER TABLE public.case_types 
ADD CONSTRAINT case_types_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for cases.company_id
ALTER TABLE public.cases 
DROP CONSTRAINT IF EXISTS cases_company_id_fkey;

ALTER TABLE public.cases 
ADD CONSTRAINT cases_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for case_tasks.company_id
ALTER TABLE public.case_tasks 
DROP CONSTRAINT IF EXISTS case_tasks_company_id_fkey;

ALTER TABLE public.case_tasks 
ADD CONSTRAINT case_tasks_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for case_time_entries.company_id
ALTER TABLE public.case_time_entries 
DROP CONSTRAINT IF EXISTS case_time_entries_company_id_fkey;

ALTER TABLE public.case_time_entries 
ADD CONSTRAINT case_time_entries_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for case_documents.company_id
ALTER TABLE public.case_documents 
DROP CONSTRAINT IF EXISTS case_documents_company_id_fkey;

ALTER TABLE public.case_documents 
ADD CONSTRAINT case_documents_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for whatsapp_conversations.company_id
ALTER TABLE public.whatsapp_conversations 
DROP CONSTRAINT IF EXISTS whatsapp_conversations_company_id_fkey;

ALTER TABLE public.whatsapp_conversations 
ADD CONSTRAINT whatsapp_conversations_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Add foreign key constraint for task_reminders.company_id
ALTER TABLE public.task_reminders 
DROP CONSTRAINT IF EXISTS task_reminders_company_id_fkey;

ALTER TABLE public.task_reminders 
ADD CONSTRAINT task_reminders_company_id_fkey 
FOREIGN KEY (company_id) 
REFERENCES public.companies(id) 
ON DELETE CASCADE;

-- Step 4: Create indexes to improve deletion performance
CREATE INDEX IF NOT EXISTS idx_case_types_company_cascade ON public.case_types(company_id);
CREATE INDEX IF NOT EXISTS idx_cases_company_cascade ON public.cases(company_id);
CREATE INDEX IF NOT EXISTS idx_case_tasks_company_cascade ON public.case_tasks(company_id);
CREATE INDEX IF NOT EXISTS idx_case_time_entries_company_cascade ON public.case_time_entries(company_id);
CREATE INDEX IF NOT EXISTS idx_case_documents_company_cascade ON public.case_documents(company_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_conversations_company_cascade ON public.whatsapp_conversations(company_id);
CREATE INDEX IF NOT EXISTS idx_task_reminders_company_cascade ON public.task_reminders(company_id);

-- Step 5: Verify the constraints were added successfully
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name, 
    ccu.column_name AS foreign_column_name, 
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu 
    ON tc.constraint_name = kcu.constraint_name 
    AND tc.table_schema = kcu.table_schema 
JOIN information_schema.constraint_column_usage AS ccu 
    ON ccu.constraint_name = tc.constraint_name 
    AND ccu.table_schema = tc.table_schema 
JOIN information_schema.referential_constraints AS rc 
    ON tc.constraint_name = rc.constraint_name 
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND ccu.table_name = 'companies' 
    AND tc.table_name IN (
        'case_types', 
        'cases', 
        'case_tasks', 
        'case_time_entries', 
        'case_documents', 
        'whatsapp_conversations', 
        'task_reminders'
    )
ORDER BY tc.table_name;
